package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import com.drxin.bizz.domain.MaterialReceiveLog;

/**
 * 物料领取记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
public interface IMaterialReceiveLogService extends IService<MaterialReceiveLog> {

    /**
     * 查询物料领取记录
     * 
     * @param recordId 物料领取记录主键
     * @return 物料领取记录
     */
    public MaterialReceiveLog selectMaterialReceiveLogByRecordId(Long recordId);

    /**
     * 查询物料领取记录列表
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 物料领取记录集合
     */
    public List<MaterialReceiveLog> selectMaterialReceiveLogList(MaterialReceiveLog materialReceiveLog);

    /**
     * 新增物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    public int insertMaterialReceiveLog(MaterialReceiveLog materialReceiveLog);

    /**
     * 修改物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    public int updateMaterialReceiveLog(MaterialReceiveLog materialReceiveLog);

    /**
     * 批量删除物料领取记录
     * 
     * @param recordIds 需要删除的物料领取记录主键集合
     * @return 结果
     */
    public int deleteMaterialReceiveLogByRecordIds(Long[] recordIds);

    /**
     * 删除物料领取记录信息
     * 
     * @param recordId 物料领取记录主键
     * @return 结果
     */
    public int deleteMaterialReceiveLogByRecordId(Long recordId);
}
