package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import com.drxin.bizz.domain.MaterialReceiveLog;

/**
 * 物料领取记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
public interface IMaterialReceiveLogService extends IService<MaterialReceiveLog> {

    /**
     * 查询物料领取记录
     * 
     * @param recordId 物料领取记录主键
     * @return 物料领取记录
     */
    public MaterialReceiveLog selectMaterialReceiveLogByRecordId(Long recordId);

    /**
     * 查询物料领取记录列表
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 物料领取记录集合
     */
    public List<MaterialReceiveLog> selectMaterialReceiveLogList(MaterialReceiveLog materialReceiveLog);

    /**
     * 新增物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    public int insertMaterialReceiveLog(MaterialReceiveLog materialReceiveLog);

    /**
     * 修改物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    public int updateMaterialReceiveLog(MaterialReceiveLog materialReceiveLog);

    /**
     * 批量删除物料领取记录
     * 
     * @param recordIds 需要删除的物料领取记录主键集合
     * @return 结果
     */
    public int deleteMaterialReceiveLogByRecordIds(Long[] recordIds);

    /**
     * 删除物料领取记录信息
     *
     * @param recordId 物料领取记录主键
     * @return 结果
     */
    public int deleteMaterialReceiveLogByRecordId(Long recordId);

    /**
     * 用户申请领取物料
     *
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 申请结果消息
     */
    public String applyMaterial(Long userId, Long materialId);

    /**
     * 管理员确认发放物料
     *
     * @param recordId 领取记录ID
     * @param handlerId 操作人ID
     * @param handlerName 操作人姓名
     * @return 确认结果消息
     */
    public String confirmMaterial(Long recordId, Long handlerId, String handlerName);

    /**
     * 查询用户的物料领取记录列表
     *
     * @param userId 用户ID
     * @return 用户的领取记录列表
     */
    public List<MaterialReceiveLog> selectUserMaterialReceiveList(Long userId);

    /**
     * 检查用户是否已经领取过指定物料
     *
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 是否已领取
     */
    public boolean hasUserReceivedMaterial(Long userId, Long materialId);

    /**
     * 验证用户身份是否有权限申请物料
     *
     * @param userType 用户类型
     * @return 是否有权限
     */
    public boolean validateUserIdentity(String userType);
}
