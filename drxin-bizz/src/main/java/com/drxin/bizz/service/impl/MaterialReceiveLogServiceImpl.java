package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import com.drxin.bizz.domain.MaterialInfo;
import com.drxin.bizz.service.IMaterialInfoService;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.drxin.bizz.mapper.MaterialReceiveLogMapper;
import com.drxin.bizz.domain.MaterialReceiveLog;
import com.drxin.bizz.service.IMaterialReceiveLogService;

/**
 * 物料领取记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Service
public class MaterialReceiveLogServiceImpl extends ServiceImpl<MaterialReceiveLogMapper, MaterialReceiveLog>  implements IMaterialReceiveLogService {
    @Autowired
    private MaterialReceiveLogMapper materialReceiveLogMapper;

    @Autowired
    private IMaterialInfoService materialInfoService;

    /**
     * 查询物料领取记录
     * 
     * @param recordId 物料领取记录主键
     * @return 物料领取记录
     */
    @Override
    public MaterialReceiveLog selectMaterialReceiveLogByRecordId(Long recordId) {
        return materialReceiveLogMapper.selectMaterialReceiveLogByRecordId(recordId);
    }

    /**
     * 查询物料领取记录列表
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 物料领取记录
     */
    @Override
    public List<MaterialReceiveLog> selectMaterialReceiveLogList(MaterialReceiveLog materialReceiveLog) {
        return materialReceiveLogMapper.selectMaterialReceiveLogList(materialReceiveLog);
    }

    /**
     * 新增物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    @Override
    public int insertMaterialReceiveLog(MaterialReceiveLog materialReceiveLog) {
        materialReceiveLog.setCreateTime(DateUtils.getNowDate());
        return materialReceiveLogMapper.insertMaterialReceiveLog(materialReceiveLog);
    }

    /**
     * 修改物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    @Override
    public int updateMaterialReceiveLog(MaterialReceiveLog materialReceiveLog) {
        materialReceiveLog.setUpdateTime(DateUtils.getNowDate());
        return materialReceiveLogMapper.updateMaterialReceiveLog(materialReceiveLog);
    }

    /**
     * 批量删除物料领取记录
     * 
     * @param recordIds 需要删除的物料领取记录主键
     * @return 结果
     */
    @Override
    public int deleteMaterialReceiveLogByRecordIds(Long[] recordIds) {
        return materialReceiveLogMapper.deleteMaterialReceiveLogByRecordIds(recordIds);
    }

    /**
     * 删除物料领取记录信息
     *
     * @param recordId 物料领取记录主键
     * @return 结果
     */
    @Override
    public int deleteMaterialReceiveLogByRecordId(Long recordId) {
        return materialReceiveLogMapper.deleteMaterialReceiveLogByRecordId(recordId);
    }

    /**
     * 用户申请领取物料
     *
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 申请结果消息
     */
    @Override
    public String applyMaterial(Long userId, Long materialId) {
        // 1. 校验物料是否存在
        MaterialInfo materialInfo = materialInfoService.selectMaterialInfoByMaterialId(materialId);
        if (materialInfo == null) {
            throw new ServiceException("物料不存在");
        }

        // 2. 校验是否已经领取过该物料
        if (hasUserReceivedMaterial(userId, materialId)) {
            throw new ServiceException("您已经领取过该物料");
        }

        // 3. 查找待领取的记录
        QueryWrapper<MaterialReceiveLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("material_id", materialId)
                   .eq("status", "pending");
        MaterialReceiveLog existingRecord = this.getOne(queryWrapper);

        if (existingRecord == null) {
            throw new ServiceException("未找到待领取的记录");
        }

        // 4. 更新状态为申请中，记录申请时间
        existingRecord.setStatus("applying");
        existingRecord.setApplyTime(new Date());
        int result = this.updateMaterialReceiveLog(existingRecord);

        if (result <= 0) {
            throw new ServiceException("申请提交失败");
        }

        return "申请提交成功，等待管理员确认";
    }

    /**
     * 管理员确认发放物料
     *
     * @param recordId 领取记录ID
     * @param handlerId 操作人ID
     * @param handlerName 操作人姓名
     * @return 确认结果消息
     */
    @Override
    public String confirmMaterial(Long recordId, Long handlerId, String handlerName) {
        // 1. 查询记录是否存在
        MaterialReceiveLog receiveLog = this.selectMaterialReceiveLogByRecordId(recordId);
        if (receiveLog == null) {
            throw new ServiceException("领取记录不存在");
        }

        // 2. 校验当前状态是否为申请中
        if (!"applying".equals(receiveLog.getStatus())) {
            throw new ServiceException("只能确认申请中的记录");
        }

        // 3. 更新状态为已领取，记录确认时间和操作人
        receiveLog.setStatus("completed");
        receiveLog.setConfirmTime(new Date());
        receiveLog.setHandlerId(handlerId);
        receiveLog.setHandlerName(handlerName);

        int result = this.updateMaterialReceiveLog(receiveLog);

        if (result <= 0) {
            throw new ServiceException("确认发放失败");
        }

        return "确认发放成功";
    }

    /**
     * 查询用户的物料领取记录列表
     *
     * @param userId 用户ID
     * @return 用户的领取记录列表
     */
    @Override
    public List<MaterialReceiveLog> selectUserMaterialReceiveList(Long userId) {
        QueryWrapper<MaterialReceiveLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("create_time");

        return this.list(queryWrapper);
    }

    /**
     * 检查用户是否已经领取过指定物料
     *
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 是否已领取
     */
    @Override
    public boolean hasUserReceivedMaterial(Long userId, Long materialId) {
        QueryWrapper<MaterialReceiveLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("material_id", materialId)
                   .eq("status", "completed");

        return this.count(queryWrapper) > 0;
    }

    /**
     * 验证用户身份是否有权限申请物料
     *
     * @param userType 用户类型
     * @return 是否有权限
     */
    @Override
    public boolean validateUserIdentity(String userType) {
        if (StringUtils.isEmpty(userType)) {
            return false;
        }

        // 支持的身份类型：急救员、导师、弟子
        List<String> allowedTypes = Arrays.asList("aider", "mentor", "disciple");

        // 用户可能有多个身份，用逗号分隔
        String[] userTypes = userType.split(",");
        return Arrays.stream(userTypes)
                .anyMatch(type -> allowedTypes.contains(type.trim()));
    }
}
