package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import com.drxin.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.drxin.bizz.mapper.MaterialReceiveLogMapper;
import com.drxin.bizz.domain.MaterialReceiveLog;
import com.drxin.bizz.service.IMaterialReceiveLogService;

/**
 * 物料领取记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Service
public class MaterialReceiveLogServiceImpl extends ServiceImpl<MaterialReceiveLogMapper, MaterialReceiveLog>  implements IMaterialReceiveLogService {
    @Autowired
    private MaterialReceiveLogMapper materialReceiveLogMapper;

    /**
     * 查询物料领取记录
     * 
     * @param recordId 物料领取记录主键
     * @return 物料领取记录
     */
    @Override
    public MaterialReceiveLog selectMaterialReceiveLogByRecordId(Long recordId) {
        return materialReceiveLogMapper.selectMaterialReceiveLogByRecordId(recordId);
    }

    /**
     * 查询物料领取记录列表
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 物料领取记录
     */
    @Override
    public List<MaterialReceiveLog> selectMaterialReceiveLogList(MaterialReceiveLog materialReceiveLog) {
        return materialReceiveLogMapper.selectMaterialReceiveLogList(materialReceiveLog);
    }

    /**
     * 新增物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    @Override
    public int insertMaterialReceiveLog(MaterialReceiveLog materialReceiveLog) {
        materialReceiveLog.setCreateTime(DateUtils.getNowDate());
        return materialReceiveLogMapper.insertMaterialReceiveLog(materialReceiveLog);
    }

    /**
     * 修改物料领取记录
     * 
     * @param materialReceiveLog 物料领取记录
     * @return 结果
     */
    @Override
    public int updateMaterialReceiveLog(MaterialReceiveLog materialReceiveLog) {
        materialReceiveLog.setUpdateTime(DateUtils.getNowDate());
        return materialReceiveLogMapper.updateMaterialReceiveLog(materialReceiveLog);
    }

    /**
     * 批量删除物料领取记录
     * 
     * @param recordIds 需要删除的物料领取记录主键
     * @return 结果
     */
    @Override
    public int deleteMaterialReceiveLogByRecordIds(Long[] recordIds) {
        return materialReceiveLogMapper.deleteMaterialReceiveLogByRecordIds(recordIds);
    }

    /**
     * 删除物料领取记录信息
     * 
     * @param recordId 物料领取记录主键
     * @return 结果
     */
    @Override
    public int deleteMaterialReceiveLogByRecordId(Long recordId) {
        return materialReceiveLogMapper.deleteMaterialReceiveLogByRecordId(recordId);
    }
}
