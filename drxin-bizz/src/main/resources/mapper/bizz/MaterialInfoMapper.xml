<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.MaterialInfoMapper">
    
    <resultMap type="MaterialInfo" id="MaterialInfoResult">
        <result property="materialId"    column="material_id"    />
        <result property="materialName"    column="material_name"    />
        <result property="materialDesc"    column="material_desc"    />
        <result property="instruction"    column="instruction"    />
        <result property="totalQuantity"    column="total_quantity"    />
        <result property="firstFreeFlag"    column="first_free_flag"    />
        <result property="freeRoles"    column="free_roles"    />
        <result property="create_by" column="create_by"/>
        <result property="createTime"    column="create_time"    />
        <result property="update_by" column="update_by"/>
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMaterialInfoVo">
        select material_id, material_name, material_desc, instruction, total_quantity, first_free_flag, free_roles, create_time, update_time from material_info
    </sql>

    <select id="selectMaterialInfoList" parameterType="MaterialInfo" resultMap="MaterialInfoResult">
        <include refid="selectMaterialInfoVo"/>
        <where>  
            <if test="materialName != null  and materialName != ''"> and material_name like concat('%', #{materialName}, '%')</if>
        </where>
    </select>
    
    <select id="selectMaterialInfoByMaterialId" parameterType="Long" resultMap="MaterialInfoResult">
        <include refid="selectMaterialInfoVo"/>
        where material_id = #{materialId}
    </select>

    <insert id="insertMaterialInfo" parameterType="MaterialInfo" useGeneratedKeys="true" keyProperty="materialId">
        insert into material_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialName != null and materialName != ''">material_name,</if>
            <if test="materialDesc != null">material_desc,</if>
            <if test="instruction != null">instruction,</if>
            <if test="totalQuantity != null">total_quantity,</if>
            <if test="firstFreeFlag != null">first_free_flag,</if>
            <if test="freeRoles != null">free_roles,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialName != null and materialName != ''">#{materialName},</if>
            <if test="materialDesc != null">#{materialDesc},</if>
            <if test="instruction != null">#{instruction},</if>
            <if test="totalQuantity != null">#{totalQuantity},</if>
            <if test="firstFreeFlag != null">#{firstFreeFlag},</if>
            <if test="freeRoles != null">#{freeRoles},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMaterialInfo" parameterType="MaterialInfo">
        update material_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialName != null and materialName != ''">material_name = #{materialName},</if>
            <if test="materialDesc != null">material_desc = #{materialDesc},</if>
            <if test="instruction != null">instruction = #{instruction},</if>
            <if test="totalQuantity != null">total_quantity = #{totalQuantity},</if>
            <if test="firstFreeFlag != null">first_free_flag = #{firstFreeFlag},</if>
            <if test="freeRoles != null">free_roles = #{freeRoles},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where material_id = #{materialId}
    </update>

    <delete id="deleteMaterialInfoByMaterialId" parameterType="Long">
        delete from material_info where material_id = #{materialId}
    </delete>

    <delete id="deleteMaterialInfoByMaterialIds" parameterType="String">
        delete from material_info where material_id in 
        <foreach item="materialId" collection="array" open="(" separator="," close=")">
            #{materialId}
        </foreach>
    </delete>
</mapper>
