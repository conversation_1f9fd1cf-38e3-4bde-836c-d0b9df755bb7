<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.wechat.mapper.WxMaterialReceiveLogMapper">

    <resultMap type="com.drxin.wechat.domain.WxMaterialReceiveLog" id="WxMaterialReceiveLogResult">
        <result property="recordId"    column="record_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="materialId"    column="material_id"    />
        <result property="materialName"    column="material_name"    />
        <result property="quantity"    column="quantity"    />
        <result property="status"    column="status"    />
        <result property="triggerType"    column="trigger_type"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="confirmTime"    column="confirm_time"    />
        <result property="handlerId"    column="handler_id"    />
        <result property="handlerName"    column="handler_name"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWxMaterialReceiveLogVo">
        select record_id, user_id, user_name, material_id, material_name, quantity, status, trigger_type, apply_time, confirm_time, handler_id, handler_name, remark, create_time, update_time from material_receive_log
    </sql>

    <!-- 查询用户的物料领取记录列表 -->
    <select id="selectUserMaterialReceiveList" parameterType="Long" resultMap="WxMaterialReceiveLogResult">
        <include refid="selectWxMaterialReceiveLogVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>

    <!-- 统计用户已完成领取的物料数量 -->
    <select id="countCompletedRecords" resultType="int">
        select count(1) from material_receive_log
        where user_id = #{userId} and material_id = #{materialId} and status = 'completed'
    </select>

    <!-- 查找用户待领取的记录 -->
    <select id="selectPendingRecord" resultMap="WxMaterialReceiveLogResult">
        <include refid="selectWxMaterialReceiveLogVo"/>
        where user_id = #{userId} and material_id = #{materialId} and status = 'pending'
        limit 1
    </select>

    <!-- 更新物料领取记录 -->
    <update id="updateMaterialReceiveLog" parameterType="com.drxin.wechat.domain.WxMaterialReceiveLog">
        update material_receive_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="handlerId != null">handler_id = #{handlerId},</if>
            <if test="handlerName != null and handlerName != ''">handler_name = #{handlerName},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = now()
        </trim>
        where record_id = #{recordId}
    </update>

    <!-- 统计指定物料ID的数量（检查物料是否存在） -->
    <select id="countMaterialById" parameterType="Long" resultType="int">
        select count(1) from material_info where material_id = #{materialId}
    </select>

</mapper>
