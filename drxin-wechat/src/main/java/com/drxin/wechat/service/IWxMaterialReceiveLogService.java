package com.drxin.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.wechat.domain.WxMaterialReceiveLog;
import java.util.List;

/**
 * 微信物料领取记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-27
 */
public interface IWxMaterialReceiveLogService extends IService<WxMaterialReceiveLog> {

    /**
     * 用户申请领取物料
     * 
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 申请结果消息
     */
    String applyMaterial(Long userId, Long materialId);

    /**
     * 查询用户的物料领取记录列表
     * 
     * @param userId 用户ID
     * @return 用户的领取记录列表
     */
    List<WxMaterialReceiveLog> selectUserMaterialReceiveList(Long userId);

    /**
     * 检查用户是否已经领取过指定物料
     * 
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 是否已领取
     */
    boolean hasUserReceivedMaterial(Long userId, Long materialId);

    /**
     * 验证用户身份是否有权限申请物料
     *
     * @param userType 用户类型
     * @return 是否有权限
     */
    boolean validateUserIdentity(String userType);

    /**
     * 根据记录ID获取物料领取记录
     *
     * @param recordId 记录ID
     * @return 物料领取记录
     */
    WxMaterialReceiveLog getByRecordId(Long recordId);

    /**
     * 根据用户ID和物料ID获取记录
     *
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 物料领取记录
     */
    WxMaterialReceiveLog getByUserIdAndMaterialId(Long userId, Long materialId);

    /**
     * 根据状态查询记录列表
     *
     * @param status 状态
     * @return 记录列表
     */
    List<WxMaterialReceiveLog> listByStatus(String status);

    /**
     * 批量更新记录状态
     *
     * @param recordIds 记录ID列表
     * @param status 新状态
     * @return 更新结果
     */
    boolean batchUpdateStatus(List<Long> recordIds, String status);
}
