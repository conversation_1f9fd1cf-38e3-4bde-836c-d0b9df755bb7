package com.drxin.wechat.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.drxin.bizz.domain.MaterialInfo;
import com.drxin.bizz.domain.MaterialReceiveLog;
import com.drxin.bizz.service.IMaterialInfoService;
import com.drxin.bizz.service.IMaterialReceiveLogService;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.R;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.wechat.domain.WxMaterialReceiveLog;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物料领取记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@RestController
@RequestMapping("/wx/material")
public class WxMaterialReceiveLogController extends BaseController {

    @Resource
    private IMaterialReceiveLogService materialReceiveLogService;

    @Resource
    private IMaterialInfoService materialInfoService;

    /**
     * 用户提交领取申请
     * 
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 申请结果
     */
    @PostMapping("/apply")
    public R<String> applyMaterial(@RequestParam Long userId, @RequestParam Long materialId) {
        try {
            // 1. 校验用户身份
            validateUserIdentity();
            
            // 2. 校验物料是否存在
            MaterialInfo materialInfo = materialInfoService.selectMaterialInfoByMaterialId(materialId);
            if (materialInfo == null) {
                return R.fail("物料不存在");
            }
            
            // 3. 校验是否已经领取过该物料
            if (hasUserReceivedMaterial(userId, materialId)) {
                return R.fail("您已经领取过该物料");
            }
            
            // 4. 查找待领取的记录
            QueryWrapper<MaterialReceiveLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("material_id", materialId)
                       .eq("status", "pending");
            MaterialReceiveLog existingRecord = materialReceiveLogService.getOne(queryWrapper);
            
            if (existingRecord == null) {
                return R.fail("未找到待领取的记录");
            }
            
            // 5. 更新状态为申请中，记录申请时间
            existingRecord.setStatus("applying");
            existingRecord.setApplyTime(new Date());
            materialReceiveLogService.updateMaterialReceiveLog(existingRecord);
            
            return R.ok("申请提交成功，等待管理员确认");
            
        } catch (ServiceException e) {
            return R.fail(e.getMessage());
        } catch (Exception e) {
            logger.error("提交物料领取申请失败", e);
            return R.fail("申请提交失败，请稍后重试");
        }
    }

    /**
     * 用户查看自己的领取记录
     * 
     * @return 领取记录列表
     */
    @GetMapping("/list")
    public R<List<WxMaterialReceiveLog>> getMaterialReceiveList() {
        try {
            Long userId = SecurityUtils.getUserId();
            
            // 查询用户的所有领取记录
            QueryWrapper<MaterialReceiveLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .orderByDesc("create_time");
            
            List<MaterialReceiveLog> receiveLogList = materialReceiveLogService.list(queryWrapper);
            
            // 转换为微信端的VO对象
            List<WxMaterialReceiveLog> wxReceiveLogList = receiveLogList.stream()
                    .map(this::convertToWxMaterialReceiveLog)
                    .collect(Collectors.toList());
            
            return R.ok(wxReceiveLogList);
            
        } catch (Exception e) {
            logger.error("查询物料领取记录失败", e);
            return R.fail("查询失败，请稍后重试");
        }
    }

    /**
     * 校验用户身份是否为急救员/导师/弟子
     */
    private void validateUserIdentity() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        String userType = user.getUserType();
        
        if (StringUtils.isEmpty(userType)) {
            throw new ServiceException("用户身份信息不完整");
        }
        
        // 支持的身份类型：急救员、导师、弟子
        List<String> allowedTypes = Arrays.asList("aider", "mentor", "disciple");
        
        // 用户可能有多个身份，用逗号分隔
        String[] userTypes = userType.split(",");
        boolean hasValidIdentity = Arrays.stream(userTypes)
                .anyMatch(type -> allowedTypes.contains(type.trim()));
        
        if (!hasValidIdentity) {
            throw new ServiceException("只有急救员、导师或弟子才能申请领取物料");
        }
    }

    /**
     * 检查用户是否已经领取过该物料
     * 
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 是否已领取
     */
    private boolean hasUserReceivedMaterial(Long userId, Long materialId) {
        QueryWrapper<MaterialReceiveLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("material_id", materialId)
                   .eq("status", "completed");
        
        return materialReceiveLogService.count(queryWrapper) > 0;
    }

    /**
     * 转换为微信端的VO对象
     * 
     * @param materialReceiveLog 业务对象
     * @return 微信端VO对象
     */
    private WxMaterialReceiveLog convertToWxMaterialReceiveLog(MaterialReceiveLog materialReceiveLog) {
        WxMaterialReceiveLog wxMaterialReceiveLog = new WxMaterialReceiveLog();
        BeanUtils.copyProperties(materialReceiveLog, wxMaterialReceiveLog);
        return wxMaterialReceiveLog;
    }
}
