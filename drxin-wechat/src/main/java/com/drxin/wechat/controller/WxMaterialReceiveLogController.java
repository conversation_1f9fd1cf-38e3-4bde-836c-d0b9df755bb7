package com.drxin.wechat.controller;

import com.drxin.bizz.domain.MaterialReceiveLog;
import com.drxin.bizz.service.IMaterialReceiveLogService;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.R;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.wechat.domain.WxMaterialReceiveLog;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物料领取记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@RestController
@RequestMapping("/wx/material")
public class WxMaterialReceiveLogController extends BaseController {

    @Resource
    private IMaterialReceiveLogService materialReceiveLogService;

    /**
     * 用户提交领取申请
     *
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 申请结果
     */
    @PostMapping("/apply")
    public R<String> applyMaterial(@RequestParam Long userId, @RequestParam Long materialId) {
        try {
            // 1. 校验用户身份
            LoginUser loginUser = SecurityUtils.getLoginUser();
            SysUser user = loginUser.getUser();
            String userType = user.getUserType();

            if (!materialReceiveLogService.validateUserIdentity(userType)) {
                return R.fail("只有急救员、导师或弟子才能申请领取物料");
            }

            // 2. 调用Service处理申请逻辑
            String result = materialReceiveLogService.applyMaterial(userId, materialId);
            return R.ok(result);

        } catch (ServiceException e) {
            return R.fail(e.getMessage());
        } catch (Exception e) {
            logger.error("提交物料领取申请失败", e);
            return R.fail("申请提交失败，请稍后重试");
        }
    }

    /**
     * 用户查看自己的领取记录
     *
     * @return 领取记录列表
     */
    @GetMapping("/list")
    public R<List<WxMaterialReceiveLog>> getMaterialReceiveList() {
        try {
            Long userId = SecurityUtils.getUserId();

            // 调用Service查询用户的领取记录
            List<MaterialReceiveLog> receiveLogList = materialReceiveLogService.selectUserMaterialReceiveList(userId);

            // 转换为微信端的VO对象
            List<WxMaterialReceiveLog> wxReceiveLogList = receiveLogList.stream()
                    .map(this::convertToWxMaterialReceiveLog)
                    .collect(Collectors.toList());

            return R.ok(wxReceiveLogList);

        } catch (Exception e) {
            logger.error("查询物料领取记录失败", e);
            return R.fail("查询失败，请稍后重试");
        }
    }



    /**
     * 转换为微信端的VO对象
     * 
     * @param materialReceiveLog 业务对象
     * @return 微信端VO对象
     */
    private WxMaterialReceiveLog convertToWxMaterialReceiveLog(MaterialReceiveLog materialReceiveLog) {
        WxMaterialReceiveLog wxMaterialReceiveLog = new WxMaterialReceiveLog();
        BeanUtils.copyProperties(materialReceiveLog, wxMaterialReceiveLog);
        return wxMaterialReceiveLog;
    }
}
