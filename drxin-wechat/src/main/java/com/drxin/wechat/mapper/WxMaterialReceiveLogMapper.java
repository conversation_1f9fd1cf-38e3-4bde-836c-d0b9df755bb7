package com.drxin.wechat.mapper;

import com.drxin.wechat.domain.WxMaterialReceiveLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 微信物料领取记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
public interface WxMaterialReceiveLogMapper {

    /**
     * 查询用户的物料领取记录列表
     * 
     * @param userId 用户ID
     * @return 用户的领取记录列表
     */
    List<WxMaterialReceiveLog> selectUserMaterialReceiveList(@Param("userId") Long userId);

    /**
     * 统计用户已完成领取的物料数量
     * 
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 已完成领取的数量
     */
    int countCompletedRecords(@Param("userId") Long userId, @Param("materialId") Long materialId);

    /**
     * 查找用户待领取的记录
     * 
     * @param userId 用户ID
     * @param materialId 物料ID
     * @return 待领取的记录
     */
    WxMaterialReceiveLog selectPendingRecord(@Param("userId") Long userId, @Param("materialId") Long materialId);

    /**
     * 更新物料领取记录
     * 
     * @param record 物料领取记录
     * @return 更新结果
     */
    int updateMaterialReceiveLog(WxMaterialReceiveLog record);

    /**
     * 统计指定物料ID的数量（检查物料是否存在）
     * 
     * @param materialId 物料ID
     * @return 物料数量
     */
    int countMaterialById(@Param("materialId") Long materialId);
}
